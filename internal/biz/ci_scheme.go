package biz

import (
	"context"
	"errors"
	"time"

	"github.com/samber/lo"
	"gitlab.qomolo.com/cicd/tools/devops_backend/api/devops"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qutil"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

func (uc *DevopsUsercase) IntegrationSave(ctx context.Context, req CiIntegration) (id int, checkRes *IntegrationDepsCheckRes, err error) {
	moduleIds := lo.Map(req.Modules, func(val CiIntegrationModule, index int) int64 {
		return int64(val.ModuleVersionId)
	})
	resources := req.Resources.Data()
	if len(resources.Dockers) > 0 || len(moduleIds) > 0 {
		dockersNew, err := uc.updateResourcesDockers(resources.Dockers, moduleIds)
		if err != nil {
			return 0, nil, err
		}
		resources.Dockers = dockersNew
		resources.Dockers = lo.Uniq(resources.Dockers)
		req.Resources = datatypes.NewJSONType(resources)
	}
	if len(req.Targets) == 0 || len(req.Labels) == 0 {
		schemeInfo, err := uc.SchemeInfo(ctx, req.SchemeId)
		if err != nil {
			return 0, nil, err
		}
		if len(req.Targets) == 0 {
			req.Targets = schemeInfo.Targets
		}
		if len(req.Labels) == 0 {
			req.Labels = schemeInfo.Labels
		}
	}
	depsCheck, err := uc.IntegrationDepsCheck(ctx, IntegrationDepsCheckReq{
		Modules: moduleIds,
	})
	if err != nil {
		return 0, depsCheck, err
	}
	if !depsCheck.Pass {
		return 0, depsCheck, errors.New(depsCheck.Errors.Error())
	}

	if req.Id > 0 {
		// 更新集成版本
		err = uc.tx.ExecTx(ctx, func(ctx context.Context) error {
			id1, err1 := uc.ciRepo.IntegrationUpdate(ctx, req, true)
			if err1 != nil {
				return err1
			}
			id = id1
			// 上传版本信息到nexus
			return uc.uploadIntegrationInfo(ctx, id)
		})
		if err != nil {
			return id, depsCheck, err
		} else {
			return req.Id, depsCheck, nil
		}
	} else {
		// 新增集成版本
		hasZero := lo.SomeBy(req.Modules, func(val CiIntegrationModule) bool {
			return val.ModuleVersionId == 0
		})
		if hasZero {
			return 0, depsCheck, devops.ErrorParamsError("模块选择不能为空")
		}
		// 检查该版本是否已经存在
		existInfo, err1 := uc.ciRepo.IntegrationInfo(ctx, CiIntegration{
			SchemeId:  req.SchemeId,
			ModuleIds: req.ModuleIds,
			Status:    EnableStatus,
			IsDelete:  NotDelete,
			Resources: req.Resources,
		})
		if err1 != nil && !errors.Is(err1, gorm.ErrRecordNotFound) {
			return 0, nil, err1
		}
		// nolint
		if existInfo != nil {
			// return 0, nil, devops.ErrorCiDuplicate("%s 模块组合已存在 版本号:%s", existInfo.Name, existInfo.Version)
		}

		// 校验是否都是 release 分支出来的模块
		if !req.IsTestVersion {
			schemeInfo, err := uc.ciRepo.SchemeInfo(context.Background(), CiScheme{Id: req.SchemeId})
			if err != nil {
				return 0, nil, err
			}
			if schemeInfo.Extras.Data().CheckModuleBranch {
				for _, moduleId := range moduleIds {
					moduleVersionInfo, err := uc.ModuleVersionInfo(context.Background(), CiModuleVersion{Id: int(moduleId)})
					if err != nil {
						return 0, nil, err
					}

					if !qutil.IsQpMasterBranch(moduleVersionInfo.Branch) {
						req.IsTestVersion = true
						break
					}
				}
			}
		}

		err = uc.tx.ExecTx(ctx, func(ctx context.Context) error {
			id1, err2 := uc.ciRepo.IntegrationCreate(ctx, &req)
			if err2 != nil {
				return err2
			}
			id = id1
			// 上传版本信息到nexus
			err2 = uc.uploadIntegrationInfo(ctx, id)
			if err2 != nil {
				return err2
			}
			return uc.uploadSchemeVersionRelease(ctx, req.SchemeId, req.Name)
		})
		if err != nil {
			return 0, depsCheck, err
		}
		return id, depsCheck, err
	}

}

func (uc *DevopsUsercase) IntegrationDelete(ctx context.Context, id int) error {
	info, err := uc.ciRepo.IntegrationInfo(ctx, CiIntegration{
		Id: id,
	})
	if err != nil {
		return err
	}
	err = uc.ciRepo.IntegrationDelete(ctx, id)
	if err != nil {
		return err
	}
	err = uc.uploadSchemeVersionRelease(ctx, id, info.Name)
	if err != nil {
		return err
	}
	return nil
}

func (uc *DevopsUsercase) IntegrationInfo(ctx context.Context, id int) (info *CiIntegration, err error) {
	return uc.ciRepo.IntegrationInfo(ctx, CiIntegration{Id: id})
}

func (uc *DevopsUsercase) IntegrationInfoByName(ctx context.Context, name, version, arch string) (info *CiIntegration, err error) {
	if arch != "all" {
		// arch 不为all时, 优先查找是否有all的arch
		info, err = uc.ciRepo.IntegrationInfo(ctx, CiIntegration{Name: name, Version: version, Arch: "all"})
		if err == nil || info.Id > 0 {
			if info != nil {
				info.Arch = ArchType(arch)
			}
			return
		}
	}
	info, err = uc.ciRepo.IntegrationInfo(ctx, CiIntegration{Name: name, Version: version, Arch: ArchType(arch)})
	return
}

func (uc *DevopsUsercase) IntegrationGroupListByIntegrationId(ctx context.Context, integrationId int64) ([]*CiIntegrationGroup, error) {
	return uc.ciRepo.IntegrationGroupListByIntegrationId(ctx, integrationId)
}

func (uc *DevopsUsercase) IntegrationList(ctx context.Context, req IntegrationListReq) (list []*CiIntegration, total int64, err error) {
	return uc.ciRepo.IntegrationList(ctx, req)
}
func (uc *DevopsUsercase) IntegrationUpdateType(ctx context.Context, id int, srcType, destType VersionReleaseType) (err error) {
	data, err := uc.ciRepo.IntegrationInfo(ctx, CiIntegration{Id: id})
	if err != nil {
		return err
	}
	if data.Type != srcType {
		return errors.New("current type not match")
	}
	data.Type = destType
	return uc.ciRepo.IntegrationUpdateType(ctx, id, srcType, destType)
}

func (uc *DevopsUsercase) IntegrationUpdateStatus(ctx context.Context, id int) error {
	info, err := uc.ciRepo.IntegrationInfo(ctx, CiIntegration{Id: id})
	if err != nil {
		return err
	}
	status := EnableStatus
	if info.Status == (EnableStatus) {
		status = DisableStatus
	}
	err = uc.tx.ExecTx(ctx, func(ctx context.Context) error {
		ctx, cancel := context.WithTimeout(ctx, time.Second*10)
		defer cancel()
		err1 := uc.ciRepo.IntegrationUpdateStatus(ctx, id, status)
		if err1 != nil {
			return err1
		}
		err1 = uc.uploadIntegrationInfo(ctx, id)
		if err1 != nil {
			return err1
		}
		err1 = uc.uploadSchemeVersionRelease(ctx, info.SchemeId, info.Name)
		if err1 != nil {
			return err1
		}
		return nil
	})
	return err
}

func (uc *DevopsUsercase) IntegrationDepsCheck(ctx context.Context, req IntegrationDepsCheckReq) (*IntegrationDepsCheckRes, error) {
	if len(req.Modules) == 0 {
		return &IntegrationDepsCheckRes{
			Pass:   true,
			ErrNum: 0,
		}, nil
	}
	deps, depIdMap, modules, err := uc.ciRepo.ModuleVersionDeps(ctx, req.Modules)
	if err != nil {
		return nil, err
	}
	return newModuleDepsCheck(req.Modules, deps, depIdMap, modules).check(), nil
}
